// General
"app.name" = "Livestock Manager";
"cancel" = "Cancel";
"save" = "Save";
"update" = "Update";
"edit" = "Edit";
"delete" = "Delete";
"confirm" = "Confirm";
"done" = "Done";
"unknown" = "Unknown";
"required" = "Required";

// Tab & Navigation
"tab.home" = "Home";
"tab.add" = "Add";
"tab.profile" = "Profile";
"tab.todo" = "Tasks";
"tab.search" = "Search";
"tab.tools" = "Tools";
"nav.myFarm" = "My Farm";

// Todo
"todo.title" = "Farm Tasks";
"todo.add.placeholder" = "Add a farm task...";
"todo.hide.completed" = "Hide Completed";
"todo.show.completed" = "Show Completed";
"todo.empty" = "No tasks yet";
"todo.empty.desc" = "Add some farm work tasks";
"todo.all.completed" = "All tasks completed";
"todo.view.completed" = "View Completed Tasks";
"todo.example.task1" = "Check cattle pen fence";
"todo.example.task2" = "Disinfect pig pen";
"todo.example.task3" = "Replenish feed";

// Home
"home.empty.welcome" = "Welcome to your farm!";
"home.empty.noAnimals" = "You haven't added any livestock yet";
"home.empty.startAdding" = "Click the button below to start recording your first livestock";
"home.empty.addFirst" = "Add First Livestock";
"home.features.title" = "Farm Management Features";
"home.features.records" = "Records Management";
"home.features.records.desc" = "Record detailed information about your livestock, including breed, age, health status, etc.";
"home.features.search" = "Quick Search";
"home.features.search.desc" = "Quickly find the livestock you want to check through ID, breed, and other keywords";
"home.features.filter" = "Smart Filtering";
"home.features.filter.desc" = "Filter by species, gender, status, and other conditions for quick categorized management";
"home.features.stats" = "Data Statistics";
"home.features.stats.desc" = "View real-time statistics on in-stock, for-sale, sold, and other statuses";
"home.tips.title" = "Usage Tips";
"home.tips.photos" = "Take photos of each livestock for easy identification and management";
"home.tips.ids" = "Use a consistent numbering system, like 'Cattle001', 'Sheep001', etc.";
"home.tips.updates" = "Update livestock status regularly to maintain data accuracy";
"home.tips.features" = "Make full use of search and filtering features to improve management efficiency";

// Filter
"filter.title" = "Filter Livestock";
"filter.applied" = "Filters Applied";
"filter.clear" = "Clear";
"filter.clearAll" = "Clear All Filters";
"filter.apply" = "Apply Filters";
"filter.reset" = "Reset Filters";
"filter.noResults" = "No livestock matching the filter criteria";
"filter.noResults.help" = "Try adjusting filter criteria or clear all filters";
"filter.all" = "All";
"filter.species" = "Species";
"filter.gender" = "Gender";
"filter.status" = "Status";
"filter.age" = "Age Range";
"filter.source" = "Source";
"filter.quick" = "Quick Filters";
"filter.quick.healthy" = "Healthy Livestock";
"filter.quick.healthy.desc" = "(Active Status)";
"filter.quick.forSale" = "For Sale Livestock";
"filter.quick.forSale.desc" = "(For Sale Status)";
"filter.quick.young" = "Young Livestock";
"filter.quick.young.desc" = "(Under 1 year)";

// Animal Properties
"animal.id" = "Unique ID";
"animal.species" = "Species";
"animal.breed" = "Breed";
"animal.gender" = "Gender";
"animal.birthDate" = "Birth Date";
"animal.age" = "Age";
"animal.source" = "Source";
"animal.purchaseDate" = "Purchase Date";
"animal.purchasePrice" = "Purchase Price";
"animal.status" = "Status";
"animal.notes" = "Notes";
"animal.photo" = "Livestock Photo";
"animal.addPhoto" = "Add Photo";

// Species
"species.cattle" = "Cattle";
"species.sheep" = "Sheep";
"species.pig" = "Pig";
"species.horse" = "Horse";
"species.chicken" = "Chicken";
"species.duck" = "Duck";
"species.goose" = "Goose";
"species.other" = "Other";

// Gender
"gender.male" = "Male";
"gender.female" = "Female";
"gender.castrated" = "Castrated";

// Source
"source.selfBred" = "Self-bred";
"source.purchased" = "Purchased";

// Status
"status.active" = "Active";
"status.forSale" = "For Sale";
"status.sold" = "Sold";
"status.deceased" = "Deceased";
"status.other" = "Other";

// Age Range
"ageRange.young" = "0-1 year";
"ageRange.middle" = "1-3 years";
"ageRange.adult" = "3+ years";

// Add/Edit Animal
"add.title" = "Add Livestock";
"edit.title" = "Edit Livestock";
"add.id.hint" = "e.g. ear tag number, custom ID";
"add.breed.placeholder" = "Enter breed";
"add.price.placeholder" = "Enter price";
"add.notes.placeholder" = "Enter notes";
"add.requiredFields" = "* Required fields";

// Detail View
"detail.basicInfo" = "Basic Information";
"detail.statusInfo" = "Status Information";
"detail.notes" = "Notes";
"detail.deleteConfirm" = "Confirm Delete";
"detail.deleteMessage" = "Data cannot be recovered after deletion. Do you confirm deletion?";

// Statistics
"stats.inStock" = "In Stock";
"stats.forSale" = "For Sale";
"stats.sold" = "Sold";
"stats.deceased" = "Deceased";

// Search
"search.placeholder" = "Search by ID, breed...";
"search.noResults" = "No results found";
"search.recentSearches" = "Recent Searches";
"search.clearHistory" = "Clear History";
"search.hotSearches" = "Hot Searches";
"search.quickSearch" = "Quick Search Tags";

// Errors & Alerts
"error.required" = "Please fill in required fields";
"error.title" = "Notice";

// Language Selection
"language.title" = "Select Language";
"language.close" = "Close";
"language.changed" = "Language Changed";
"language.restart" = "Please restart the app to fully apply language changes";
"language.ok" = "OK";

// Profile
"profile.title" = "Profile";
"profile.farm.management" = "Farm Management";
"profile.settings" = "Settings";
"profile.help" = "Help & Feedback";
"profile.data.backup" = "Data Backup";
"profile.data.export" = "Data Export";
"profile.statistics" = "Statistics Report";
"profile.notification" = "Notification Settings";
"profile.language" = "Language Settings";
"profile.privacy" = "Privacy Policy";
"profile.about" = "Version";
"profile.version" = "Version %@";
"profile.help.usage" = "Usage Help";
"profile.rate" = "Rate Us";
"profile.contact" = "Contact Support";
"profile.user.title" = "Farmer";
"profile.user.days" = "Days used: %d days";
"profile.user.phone" = "Phone: %@";
"profile.total.animals" = "Total Livestock";
"profile.animals.instock" = "In Stock";
"profile.animals.added" = "Added This Month";
"profile.edit" = "Edit Profile";
"profile.back" = "Back";

// Notification Settings
"notification.title" = "Notification Settings";
"notification.daily" = "Daily Reminder";
"notification.time" = "Reminder Time";
"notification.system.settings" = "System Settings";
"notification.system.open" = "Open System Notification Settings";
"notification.about" = "About Notifications";
"notification.content" = "Notification Content";
"notification.message" = "Daily reminder will notify you at the set time: \"Time to manage your animals!\"";
"notification.permission.denied" = "Notification Permission Denied";
"notification.permission.message" = "Please allow notifications for this app in device settings";
"notification.goto.settings" = "Go to Settings";
"notification.system.hint" = "If you're not receiving notifications, ensure Livestock Manager is allowed to send notifications in system settings";

// Email Popup
"email.contact" = "Contact Support";
"email.copy" = "Copy Email";
"email.copied" = "Copied";
"email.close" = "Close";

// Usage Guide
"usage.guide.title" = "Usage Guide";
"usage.guide.close" = "Close";
"usage.guide.content.1" = "1. Home Page\n• View all livestock list\n• Use search to quickly find specific livestock\n• Use filters to view livestock by conditions";
"usage.guide.content.2" = "2. Add Livestock\n• Click the 'Add' button at the bottom\n• Fill in basic livestock information\n• Optionally add photos\n• Click save when done";
"usage.guide.content.3" = "3. Livestock Management\n• Click livestock card to view details\n• Edit or delete livestock information\n• Update livestock status timely";
"usage.guide.content.4" = "4. Todo Tasks\n• Add work reminders in 'Tasks' page\n• Mark as complete when done\n• Option to hide completed tasks";
"usage.guide.content.5" = "5. Notifications\n• Enable notifications in settings\n• Set daily reminder time\n• Never miss important work";
"usage.guide.content.6" = "6. Data Security\n• Regularly check data accuracy\n• Save important information timely\n• Contact support if issues arise";

// Tools
"tools.title" = "Tools";
"tools.counter" = "Counter";
"tools.calculator" = "Farm Calculator";
"tools.weather" = "Weather";
"tools.marketPrice" = "Market Price";
"tools.timer" = "Timer";
"tools.notes" = "Notes";
"tools.calendar" = "Calendar";
"tools.converter" = "Unit Converter";
"tools.medicine" = "Medicine";
"tools.basicTools" = "Basic Tools";
"tools.suggestions" = "User Suggestions";
"tools.suggestNewTool" = "Suggest New Tool";

// Counter Tool
"tools.counter.start" = "Start New Count";
"tools.counter.reset" = "Reset";
"tools.counter.complete" = "Complete Count";
"tools.counter.counting" = "Counting";
"tools.counter.startTime" = "Start Time: %@";
"tools.counter.new" = "Start a New Count";
"tools.counter.history" = "Count History";
"tools.counter.save" = "Save Count";
"tools.counter.name" = "Count Name";
"tools.counter.note" = "Notes";
"tools.counter.date" = "Date";
"tools.counter.count" = "Count";
"tools.counter.noHistory" = "No count history yet";
"tools.counter.deleteHistory" = "Delete History";

// Counter Tool Record
"tools.counter.record.title" = "Record Details";
"tools.counter.record.details" = "Record Details";
"tools.counter.record.count" = "Count";
"tools.counter.record.namePlaceholder" = "Enter count name";
"tools.counter.record.notePlaceholder" = "Enter notes";

// Counter Tool History
"tools.counter.history.title" = "Count History";
"tools.counter.history.empty" = "No count history";
"tools.counter.history.countLabel" = "Count: %lld";
"tools.counter.history.timeLabel" = "Time: %@ - %@";
"tools.counter.history.noteLabel" = "Notes: %@";

// Unit Converter Tool
"tools.converter.value" = "Value";
"tools.converter.from" = "From";
"tools.converter.to" = "To";
"tools.converter.result" = "Conversion Result";
"tools.converter.reference" = "Common Conversion Reference";

// Converter Categories
"tools.converter.area" = "Area";
"tools.converter.weight" = "Weight";
"tools.converter.volume" = "Volume";
"tools.converter.length" = "Length";
"tools.converter.temperature" = "Temperature";

// Converter Units
"tools.converter.unit.squareMeter" = "Square Meter";
"tools.converter.unit.mu" = "Mu";
"tools.converter.unit.hectare" = "Hectare";
"tools.converter.unit.squareKilometer" = "Square Kilometer";
"tools.converter.unit.acre" = "Acre";

"tools.converter.unit.kilogram" = "Kilogram";
"tools.converter.unit.gram" = "Gram";
"tools.converter.unit.jin" = "Jin";
"tools.converter.unit.ton" = "Ton";
"tools.converter.unit.pound" = "Pound";

"tools.converter.unit.liter" = "Liter";
"tools.converter.unit.milliliter" = "Milliliter";
"tools.converter.unit.cubicMeter" = "Cubic Meter";
"tools.converter.unit.gallon" = "Gallon";

"tools.converter.unit.meter" = "Meter";
"tools.converter.unit.centimeter" = "Centimeter";
"tools.converter.unit.kilometer" = "Kilometer";
"tools.converter.unit.foot" = "Foot";
"tools.converter.unit.inch" = "Inch";

"tools.converter.unit.celsius" = "Celsius";
"tools.converter.unit.fahrenheit" = "Fahrenheit";
"tools.converter.unit.kelvin" = "Kelvin";

// Converter Reference
"tools.converter.reference.muToSqM" = "1 mu ≈ 666.67 square meters";
"tools.converter.reference.hectareToSqM" = "1 hectare = 10,000 square meters";
"tools.converter.reference.acreToSqM" = "1 acre ≈ 4,046.86 square meters";
"tools.converter.reference.kgToJin" = "1 kilogram = 2 jin";
"tools.converter.reference.tonToKg" = "1 ton = 1,000 kilograms";
"tools.converter.reference.poundToKg" = "1 pound ≈ 0.453592 kilograms";
"tools.converter.reference.literToMl" = "1 liter = 1,000 milliliters";
"tools.converter.reference.gallonToLiter" = "1 gallon (US) ≈ 3.785 liters";
"tools.converter.reference.kmToM" = "1 kilometer = 1,000 meters";
"tools.converter.reference.footToM" = "1 foot ≈ 0.3048 meters";
"tools.converter.reference.inchToCm" = "1 inch = 2.54 centimeters";
"tools.converter.reference.celsiusToFahrenheit" = "0°C = 32°F (freezing point)";
"tools.converter.reference.fahrenheitToCelsius" = "32°F = 0°C (freezing point)";
"tools.converter.reference.celsiusToKelvin" = "0°C = 273.15K (absolute zero + 273.15)";

// Weather Tool
"tools.weather.locationPermissionTitle" = "Location Permission Required";
"tools.weather.needsPermissionMessage" = "We need access to your location to provide weather information for your area. Please allow location access in settings.";
"tools.weather.farmingSuggestionTitle" = "Farming Suggestions";
"tools.weather.loading" = "Loading weather data...";
"tools.weather.errorUnknown" = "Unknown error";
"tools.weather.errorLocation" = "Unable to access location permission";
"tools.weather.refresh" = "Refresh";
"tools.weather.hourlyForecast" = "Hourly Forecast";
"tools.weather.dailyForecast" = "Daily Forecast";
"tools.weather.farmingSuggestions" = "Farming Suggestions";
"tools.weather.goToSettings" = "Go to Settings";
"tools.weather.needsPermission" = "Location permission is needed to get weather for your area";
"tools.weather.locationPermission" = "Location Permission";
"tools.weather.cancel" = "Cancel";
"tools.weather.feelsLike" = "Feels Like";
"tools.weather.humidity" = "Humidity";
"tools.weather.wind" = "Wind";
"tools.weather.uvIndex" = "UV Index";
"tools.weather.visibility" = "Visibility";
"tools.weather.precipitation" = "Precipitation";
"tools.weather.today" = "Today";
"tools.weather.tomorrow" = "Tomorrow";
"tools.weather.noData" = "No weather data available";

// Weather Tool Additional Keys
"tools.weather.errorLocationUnavailable" = "Location information unavailable";
"tools.weather.errorManagerFailed" = "Location manager error";
"tools.weather.errorFetch" = "Failed to fetch weather information";
"tools.weather.gettingLocation" = "Getting location...";
"tools.weather.unknownLocation" = "Unknown location";
"tools.weather.noSpecificSuggestionMessage" = "No specific farming suggestions at this time";

// Weather Tool - UV Index
"tools.weather.uv.low" = "Low";
"tools.weather.uv.moderate" = "Moderate";
"tools.weather.uv.high" = "High";
"tools.weather.uv.veryHigh" = "Very High";
"tools.weather.uv.extreme" = "Extreme";

// Weather Tool - Farming Suggestions
"tools.weather.suggestion.uvHigh" = "UV index is high, take sun protection measures and protect livestock";
"tools.weather.suggestion.heavyRainToday" = "Heavy rain today, please ensure livestock rain protection";
"tools.weather.suggestion.strongWindToday" = "Strong winds today, recommend securing shelter facilities";
"tools.weather.suggestion.rainNext3Days" = "Rain expected in the next three days, prepare rain protection measures";
"tools.weather.suggestion.heatWave" = "High temperatures expected in coming days, maintain cooling measures";
"tools.weather.suggestion.coldSpell" = "Low temperatures expected in coming days, ensure warmth protection";
"tools.weather.suggestion.irrigation" = "No recent rain, consider irrigating pastures";

// Farm Calculator Tool
"tools.calculator.feed" = "Feed Calculator";
"tools.calculator.breeding" = "Breeding Calculator";
"tools.calculator.cost" = "Cost Analyzer";
"tools.calculator.land" = "Land Management";
"tools.calculator.production" = "Production Tracker";

"tools.calculator.feed.title" = "Feed Requirement Calculator";
"tools.calculator.feed.description" = "Calculate daily feed requirements based on animal type, age, and weight";
"tools.calculator.animalType" = "Animal Type";
"tools.calculator.animalAge" = "Age (months)";
"tools.calculator.animalWeight" = "Weight (kg)";
"tools.calculator.animalCount" = "Number of Animals";
"tools.calculator.feedRequirement" = "Daily Feed Requirement";
"tools.calculator.feedType" = "Feed Type";
"tools.calculator.calculate" = "Calculate";
"tools.calculator.result" = "Result";

// Breeding Calculator
"tools.calculator.breeding.title" = "Breeding Calculator";
"tools.calculator.breeding.description" = "Calculate animal gestation period and expected due date";
"tools.calculator.breeding.date" = "Breeding Date";
"tools.calculator.breeding.dueDate" = "Expected Due Date";
"tools.calculator.breeding.remainingDays" = "Days Remaining";
"tools.calculator.breeding.gestationProgress" = "Gestation Progress";
"tools.calculator.breeding.keyDates" = "Key Dates";
"tools.calculator.feeding.advice" = "Feeding Advice";

// Other Calculators
"tools.calculator.cost.description" = "Analyze feed, care, and sales costs";
"tools.calculator.land.description" = "Calculate fertilizer needs and seeding rates";
"tools.calculator.production.description" = "Track milk production and egg laying";

// Calculator Units
"tools.calculator.unit.days" = "days";
"tools.calculator.unit.months" = "months";
"tools.calculator.unit.kg" = "kg";
"tools.calculator.unit.weekly" = "Weekly Requirement:";
"tools.calculator.unit.monthly" = "Monthly Requirement:";
"tools.calculator.singleAnimal" = "Single Animal:";
"tools.calculator.allAnimals" = "All Animals:";

// Animal Types
"tools.calculator.animal.cattle" = "Cattle";
"tools.calculator.animal.sheep" = "Sheep";
"tools.calculator.animal.pig" = "Pig";
"tools.calculator.animal.chicken" = "Chicken";
"tools.calculator.animal.duck" = "Duck";
"tools.calculator.animal.horse" = "Horse";
"tools.calculator.animal.goat" = "Goat";

// Feed Types
"tools.calculator.feed.grain" = "Grain";
"tools.calculator.feed.hay" = "Hay";
"tools.calculator.feed.silage" = "Silage";
"tools.calculator.feed.concentrate" = "Concentrate"; 